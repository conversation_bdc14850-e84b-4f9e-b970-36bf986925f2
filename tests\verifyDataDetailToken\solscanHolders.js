const axios = require('axios');

// === CONFIGURATION ===
const CONFIG = {
    TOKEN_ADDRESS: '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao',
    TIMEOUT: 30000, // 30 seconds
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000, // 1 second
    PAGE_SIZE: 20,
    PAGE: 1
};

// === API CLIENT ===
class APIClient {
    constructor(baseURL, defaultHeaders = {}) {
        this.client = axios.create({
            baseURL,
            timeout: CONFIG.TIMEOUT,
            headers: defaultHeaders
        });
    }

    async request(config, retries = CONFIG.MAX_RETRIES) {
        try {
            const response = await this.client(config);
            return response.data;
        } catch (error) {
            if (retries > 0 && this.shouldRetry(error)) {
                console.warn(`🔄 Retrying request... (${CONFIG.MAX_RETRIES - retries + 1}/${CONFIG.MAX_RETRIES})`);
                await this.delay(CONFIG.RETRY_DELAY);
                return this.request(config, retries - 1);
            }
            throw error;
        }
    }

    shouldRetry(error) {
        return error.code === 'ECONNABORTED' ||
               (error.response && [429, 500, 502, 503, 504].includes(error.response.status));
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Solscan API Client với headers từ cURL command
const solscanClient = new APIClient('https://api-v2.solscan.io', {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
    'origin': 'https://solscan.io',
    'priority': 'u=1, i',
    'referer': 'https://solscan.io/',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'Cookie': '_ga=GA1.1.2046817562.1732854450; cf_clearance=TIZl8BDJ547zODZm.V9vwqisnoScvZnTff5rqMgnozA-1755594062-*******-qNH22Po1ZpgDDLwvVmTehMON1t18_rkGXALBi4Wup8tyP.n9D9_FajJ.5njW.vd4WgXd5Hjs0ApkyrL_cW1Vt83YzkoHuxK_ZirrFfK1YlGmiuet52Fukc1HPyzgCRlA3YbKL5gd47ikTe3UNcLdELK9Th_PmuDz54QrzlBHYqB8jw5UQD1eLkmHdv4jY1ncMcO.FjFWIIFAe9eWf0UVu.MCd9uHnuJeoW.oLj1cnWQ; _ga_PS3V7B7KV0=GS2.1.s1755762865$o281$g1$t1755765639$j58$l0$h0'
});

// === SOLSCAN SERVICE ===
class SolscanService {
    static async getTokenHolders(address, pageSize = CONFIG.PAGE_SIZE, page = CONFIG.PAGE) {
        return await solscanClient.request({
            method: 'GET',
            url: `/v2/token/holders?address=${address}&page_size=${pageSize}&page=${page}`
        });
    }
}

// === DATA PROCESSOR ===
class HoldersProcessor {
    static processHoldersData(response) {
        if (!response?.success || !response?.data?.length) {
            console.log("⚠️ Không tìm thấy dữ liệu holders từ Solscan API");
            return [];
        }

        return response.data.map((holder, index) => ({
            rank: holder.rank || index + 1,
            address: holder.address,
            owner: holder.owner,
            amount: holder.amount,
            decimals: holder.decimals,
            withheldAmount: holder.withheldAmount || 0,
            // Tính phần trăm nắm giữ (amount / total supply)
            // Note: Cần total supply để tính chính xác, tạm thời để amount
            rawAmount: holder.amount
        }));
    }

    static displayHoldersInfo(holdersData) {
        if (!holdersData?.length) {
            console.log("❌ Không có dữ liệu holders để hiển thị");
            return;
        }

        console.log(`\n📊 === THÔNG TIN ${holdersData.length} HOLDERS ===\n`);
        
        holdersData.forEach((holder, index) => {
            console.log(`${index + 1}. Rank: ${holder.rank}`);
            console.log(`   Address: ${holder.address}`);
            console.log(`   Owner: ${holder.owner}`);
            console.log(`   Amount: ${holder.amount.toLocaleString()}`);
            console.log(`   Decimals: ${holder.decimals}`);
            console.log(`   Withheld Amount: ${holder.withheldAmount.toLocaleString()}`);
            console.log(`   ─────────────────────────────────────────`);
        });

        console.log(`\n💡 Tổng số holders: ${holdersData.length}`);
        console.log("=======================================\n");
    }
}

// === MAIN APPLICATION ===
class SolscanHoldersAnalyzer {
    static async analyze(tokenAddress = CONFIG.TOKEN_ADDRESS) {
        try {
            console.log(`🔄 Bắt đầu lấy dữ liệu holders cho token: ${tokenAddress}...`);
            console.log(`📡 Đang gọi Solscan API...`);

            // Gọi API Solscan để lấy dữ liệu holders
            const holdersResponse = await SolscanService.getTokenHolders(tokenAddress);

            console.log("✅ Lấy dữ liệu thành công, đang xử lý...");

            // Xử lý dữ liệu
            const holdersData = HoldersProcessor.processHoldersData(holdersResponse);

            // Hiển thị kết quả
            HoldersProcessor.displayHoldersInfo(holdersData);

            console.log("✅ Phân tích hoàn tất!");
            return holdersData;

        } catch (error) {
            this.handleError(error);
            throw error;
        }
    }

    static handleError(error) {
        console.error("❌ Đã xảy ra lỗi!");
        
        if (error.response) {
            console.error(`API Error: ${error.response.status} ${error.response.statusText}`);
            console.error(`URL: ${error.config?.url}`);
            if (error.response.data) {
                console.error('Response:', JSON.stringify(error.response.data, null, 2));
            }
        } else if (error.request) {
            console.error('Network Error: Không nhận được phản hồi từ server');
        } else {
            console.error('Error:', error.message);
        }
    }
}

// === EXECUTION ===
if (require.main === module) {
    SolscanHoldersAnalyzer.analyze().catch(() => process.exit(1));
}

module.exports = { SolscanHoldersAnalyzer, CONFIG };
