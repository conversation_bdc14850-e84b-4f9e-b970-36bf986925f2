const axios = require('axios');

// === CONFIGURATION ===
const CONFIG = {
    TOKEN_ADDRESS: '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao',
    TIMEOUT: 30000, // 30 seconds
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000, // 1 second
    PAGE_SIZE: 20,
    PAGE: 1
};

// === API CLIENT ===
class APIClient {
    constructor(baseURL, defaultHeaders = {}) {
        this.client = axios.create({
            baseURL,
            timeout: CONFIG.TIMEOUT,
            headers: defaultHeaders
        });
    }

    async request(config, retries = CONFIG.MAX_RETRIES) {
        try {
            const response = await this.client(config);
            return response.data;
        } catch (error) {
            if (retries > 0 && this.shouldRetry(error)) {
                console.warn(`🔄 Retrying request... (${CONFIG.MAX_RETRIES - retries + 1}/${CONFIG.MAX_RETRIES})`);
                await this.delay(CONFIG.RETRY_DELAY);
                return this.request(config, retries - 1);
            }
            throw error;
        }
    }

    shouldRetry(error) {
        return error.code === 'ECONNABORTED' ||
               (error.response && [429, 500, 502, 503, 504].includes(error.response.status));
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Solscan API Client với headers từ cURL command
const solscanClient = new APIClient('https://api-v2.solscan.io', {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
    'origin': 'https://solscan.io',
    'priority': 'u=1, i',
    'referer': 'https://solscan.io/',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'Cookie': '_ga=GA1.1.2046817562.1732854450; cf_clearance=TIZl8BDJ547zODZm.V9vwqisnoScvZnTff5rqMgnozA-**********-*******-qNH22Po1ZpgDDLwvVmTehMON1t18_rkGXALBi4Wup8tyP.n9D9_FajJ.5njW.vd4WgXd5Hjs0ApkyrL_cW1Vt83YzkoHuxK_ZirrFfK1YlGmiuet52Fukc1HPyzgCRlA3YbKL5gd47ikTe3UNcLdELK9Th_PmuDz54QrzlBHYqB8jw5UQD1eLkmHdv4jY1ncMcO.FjFWIIFAe9eWf0UVu.MCd9uHnuJeoW.oLj1cnWQ; _ga_PS3V7B7KV0=GS2.1.s1755762865$o281$g1$t1755765639$j58$l0$h0'
});

// === SOLSCAN SERVICE ===
class SolscanService {
    static async getTokenInfo(address) {
        return await solscanClient.request({
            method: 'GET',
            url: `/v2/account?address=${address}&view_as=token`
        });
    }

    static async getTokenHolders(address, pageSize = CONFIG.PAGE_SIZE, page = CONFIG.PAGE) {
        return await solscanClient.request({
            method: 'GET',
            url: `/v2/token/holders?address=${address}&page_size=${pageSize}&page=${page}`
        });
    }
}

// === DATA PROCESSOR ===
class HoldersProcessor {
    static processHoldersData(response, totalSupply) {
        if (!response?.success || !response?.data?.length) {
            console.log("⚠️ Không tìm thấy dữ liệu holders từ Solscan API");
            return [];
        }

        // Danh sách các ví cần loại bỏ khỏi tính toán
        const excludedWallets = [
            '********************************************', // ví pumpfun
            'HLnpSz9h2S4hiLQ43rnSD9XkcUThA7B8hQMKmDaiTLcC'
        ];

        return response.data
            .filter(holder => !excludedWallets.includes(holder.owner))
            .map((holder, index) => {
                // Tính % holding theo công thức: % holding = (Amount / (10^Decimals)) / Supply * 100
                const amount = parseFloat(holder.amount) || 0;
                const decimals = holder.decimals || 6;
                const adjustedAmount = amount / Math.pow(10, decimals);
                const holdingPct = totalSupply > 0 ? (adjustedAmount / totalSupply) * 100 : 0;

                return {
                    rank: holder.rank || index + 1,
                    address: holder.address,
                    owner: holder.owner,
                    amount: holder.amount,
                    decimals: holder.decimals,
                    withheldAmount: holder.withheldAmount || 0,
                    adjustedAmount,
                    holdingPct
                };
            });
    }

    static displayHoldersInfo(holdersData, totalSupply) {
        if (!holdersData?.length) {
            console.log("❌ Không có dữ liệu holders để hiển thị");
            return;
        }

        console.log(`\n📊 === TOP 10 HOLDERS (Đã loại bỏ ví blacklist) ===`);
        console.log(`📈 Total Supply: ${totalSupply?.toLocaleString()}\n`);

        const top10 = holdersData.slice(0, 10);
        let totalTop10Holdings = 0;

        top10.forEach((holder, index) => {
            totalTop10Holdings += holder.holdingPct;

            console.log(`${index + 1}. ${holder.owner?.substring(0, 8)}...${holder.owner?.slice(-8)}: ${holder.holdingPct.toFixed(4)}%`);
            console.log(`   Amount: ${holder.amount.toLocaleString()}`);
            console.log(`   Decimals: ${holder.decimals}`);
            console.log(`   Adjusted Amount: ${holder.adjustedAmount.toLocaleString()}`);
            console.log(`   ─────────────────────────────────────────`);
        });

        console.log(`\n💰 Tổng % nắm giữ của Top 10: ${totalTop10Holdings.toFixed(4)}%`);
        console.log("=======================================\n");

        return totalTop10Holdings;
    }
}

// === MAIN APPLICATION ===
class SolscanHoldersAnalyzer {
    static async analyze(tokenAddress = CONFIG.TOKEN_ADDRESS) {
        try {
            console.log(`🔄 Bắt đầu lấy dữ liệu holders cho token: ${tokenAddress}...`);
            console.log(`📡 Đang gọi Solscan API...`);

            // Gọi API Solscan để lấy dữ liệu token info và holders
            const [tokenInfoResponse, holdersResponse] = await Promise.all([
                SolscanService.getTokenInfo(tokenAddress),
                SolscanService.getTokenHolders(tokenAddress)
            ]);

            console.log("✅ Lấy dữ liệu thành công, đang xử lý...");

            // Tính total supply từ token info
            const tokenData = tokenInfoResponse?.data;
            const tokenInfo = tokenData?.tokenInfo || {};
            const decimals = tokenInfo.decimals || 6;
            const rawSupply = tokenInfo.supply ? parseInt(tokenInfo.supply) : 0;
            const totalSupply = decimals > 0 ? rawSupply / Math.pow(10, decimals) : rawSupply;

            console.log(`📊 Token Info - Supply: ${rawSupply.toLocaleString()}, Decimals: ${decimals}, Total Supply: ${totalSupply.toLocaleString()}`);

            // Xử lý dữ liệu holders
            const holdersData = HoldersProcessor.processHoldersData(holdersResponse, totalSupply);

            // Hiển thị kết quả
            const top10Percentage = HoldersProcessor.displayHoldersInfo(holdersData, totalSupply);

            console.log("✅ Phân tích hoàn tất!");
            return { holdersData, totalSupply, top10Percentage };

        } catch (error) {
            this.handleError(error);
            throw error;
        }
    }

    static handleError(error) {
        console.error("❌ Đã xảy ra lỗi!");
        
        if (error.response) {
            console.error(`API Error: ${error.response.status} ${error.response.statusText}`);
            console.error(`URL: ${error.config?.url}`);
            if (error.response.data) {
                console.error('Response:', JSON.stringify(error.response.data, null, 2));
            }
        } else if (error.request) {
            console.error('Network Error: Không nhận được phản hồi từ server');
        } else {
            console.error('Error:', error.message);
        }
    }
}

// === EXECUTION ===
if (require.main === module) {
    SolscanHoldersAnalyzer.analyze().catch(() => process.exit(1));
}

module.exports = { SolscanHoldersAnalyzer, CONFIG };
